// 会员订阅系统 - Subscription system for premium features
// Subscription system for premium features with credit management

export interface SubscriptionPlan {
  id: string;
  name: string;
  nameZh: string;
  price: number;
  yearlyPrice?: number;
  currency: string;
  credits: number;
  features: string[];
  featuresZh: string[];
  popular?: boolean;
  stripePriceId?: string;
  stripeYearlyPriceId?: string;
}

export interface UserSubscription {
  id: string;
  userId: string;
  planId: string;
  status: 'active' | 'inactive' | 'cancelled' | 'expired';
  credits: number;
  maxCredits: number;
  startDate: string;
  endDate?: string;
  autoRenew: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreditUsage {
  id: string;
  userId: string;
  amount: number;
  type: 'generation' | 'bonus' | 'purchase' | 'refund';
  description: string;
  descriptionZh: string;
  metadata?: {
    styleCount?: number;
    generationId?: string;
    orderId?: string;
    planId?: string;
  };
  createdAt: string;
}

// 订阅计划配置 - Subscription plans configuration
export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'free',
    name: 'Free Trial',
    nameZh: '免费试用',
    price: 0,
    currency: 'USD',
    credits: 3,
    features: [
      '3 AI wedding photo generations',
      'Basic styles (Chinese Traditional, Western Elegant)',
      'Standard quality output',
      'Basic customer support'
    ],
    featuresZh: [
      '3次AI婚纱照生成',
      '基础风格（中式传统、西式优雅）',
      '标准质量输出',
      '基础客户支持'
    ]
  },
  {
    id: 'basic',
    name: 'Basic Plan',
    nameZh: '基础套餐',
    price: 9.99,
    yearlyPrice: 79.99,
    currency: 'USD',
    credits: 20,
    features: [
      '20 AI wedding photo generations',
      'All 6 premium styles',
      'High quality output (1024x1024)',
      'Priority processing',
      'Email support'
    ],
    featuresZh: [
      '20次AI婚纱照生成',
      '全部6种高级风格',
      '高质量输出 (1024x1024)',
      '优先处理',
      '邮件支持'
    ],
    stripePriceId: 'price_basic_monthly',
    stripeYearlyPriceId: 'price_basic_yearly'
  },
  {
    id: 'pro',
    name: 'Pro Plan',
    nameZh: '专业套餐',
    price: 19.99,
    yearlyPrice: 159.99,
    currency: 'USD',
    credits: 50,
    popular: true,
    features: [
      '50 AI wedding photo generations',
      'All premium styles + exclusive styles',
      'Ultra high quality output (2048x2048)',
      'Instant processing',
      'Style customization',
      'Batch processing',
      'Priority support'
    ],
    featuresZh: [
      '50次AI婚纱照生成',
      '全部高级风格 + 独家风格',
      '超高质量输出 (2048x2048)',
      '即时处理',
      '风格定制',
      '批量处理',
      '优先支持'
    ],
    stripePriceId: 'price_pro_monthly',
    stripeYearlyPriceId: 'price_pro_yearly'
  },
  {
    id: 'premium',
    name: 'Premium Plan',
    nameZh: '高级套餐',
    price: 39.99,
    yearlyPrice: 319.99,
    currency: 'USD',
    credits: 150,
    features: [
      'Unlimited AI wedding photo generations',
      'All styles + custom style creation',
      'Ultra high quality + RAW format',
      'Instant processing',
      'Advanced customization',
      'Bulk download',
      'White-label options',
      '24/7 priority support'
    ],
    featuresZh: [
      '无限次AI婚纱照生成',
      '全部风格 + 自定义风格创建',
      '超高质量 + RAW格式',
      '即时处理',
      '高级定制',
      '批量下载',
      '白标选项',
      '24/7优先支持'
    ],
    stripePriceId: 'price_premium_monthly',
    stripeYearlyPriceId: 'price_premium_yearly'
  }
];

// 获取用户订阅信息 - Get user subscription info
export async function getUserSubscription(userId: string): Promise<UserSubscription | null> {
  try {
    // 在实际应用中，这里会调用数据库或API
    // In a real app, this would call database or API
    const stored = localStorage.getItem(`subscription_${userId}`);
    if (stored) {
      return JSON.parse(stored);
    }
    
    // 返回默认免费订阅 - Return default free subscription
    const freeSubscription: UserSubscription = {
      id: `sub_${Date.now()}`,
      userId,
      planId: 'free',
      status: 'active',
      credits: 3,
      maxCredits: 3,
      startDate: new Date().toISOString(),
      autoRenew: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    localStorage.setItem(`subscription_${userId}`, JSON.stringify(freeSubscription));
    return freeSubscription;
  } catch (error) {
    console.error('Error getting user subscription:', error);
    return null;
  }
}

// 检查用户是否有足够的积分 - Check if user has enough credits
export async function hasEnoughCredits(userId: string, requiredCredits: number = 1): Promise<boolean> {
  const subscription = await getUserSubscription(userId);
  if (!subscription) return false;
  
  return subscription.credits >= requiredCredits;
}

// 消费积分 - Consume credits
export async function consumeCredits(
  userId: string, 
  amount: number, 
  description: string,
  descriptionZh: string,
  metadata?: any
): Promise<boolean> {
  try {
    const subscription = await getUserSubscription(userId);
    if (!subscription || subscription.credits < amount) {
      return false;
    }
    
    // 更新订阅积分 - Update subscription credits
    subscription.credits -= amount;
    subscription.updatedAt = new Date().toISOString();
    localStorage.setItem(`subscription_${userId}`, JSON.stringify(subscription));
    
    // 记录积分使用 - Record credit usage
    const usage: CreditUsage = {
      id: `usage_${Date.now()}`,
      userId,
      amount: -amount,
      type: 'generation',
      description,
      descriptionZh,
      metadata,
      createdAt: new Date().toISOString()
    };
    
    const usageHistory = getCreditUsageHistory(userId);
    usageHistory.unshift(usage);
    localStorage.setItem(`credit_usage_${userId}`, JSON.stringify(usageHistory.slice(0, 100))); // Keep last 100 records
    
    return true;
  } catch (error) {
    console.error('Error consuming credits:', error);
    return false;
  }
}

// 获取积分使用历史 - Get credit usage history
export function getCreditUsageHistory(userId: string): CreditUsage[] {
  try {
    const stored = localStorage.getItem(`credit_usage_${userId}`);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Error getting credit usage history:', error);
    return [];
  }
}

// 升级订阅 - Upgrade subscription
export async function upgradeSubscription(userId: string, planId: string): Promise<boolean> {
  try {
    const plan = SUBSCRIPTION_PLANS.find(p => p.id === planId);
    if (!plan) return false;
    
    const newSubscription: UserSubscription = {
      id: `sub_${Date.now()}`,
      userId,
      planId,
      status: 'active',
      credits: plan.credits,
      maxCredits: plan.credits,
      startDate: new Date().toISOString(),
      endDate: planId === 'free' ? undefined : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      autoRenew: planId !== 'free',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    localStorage.setItem(`subscription_${userId}`, JSON.stringify(newSubscription));
    
    // 记录积分购买 - Record credit purchase
    if (plan.credits > 0) {
      const usage: CreditUsage = {
        id: `usage_${Date.now()}`,
        userId,
        amount: plan.credits,
        type: 'purchase',
        description: `Purchased ${plan.name} plan`,
        descriptionZh: `购买${plan.nameZh}套餐`,
        metadata: { planId },
        createdAt: new Date().toISOString()
      };
      
      const usageHistory = getCreditUsageHistory(userId);
      usageHistory.unshift(usage);
      localStorage.setItem(`credit_usage_${userId}`, JSON.stringify(usageHistory.slice(0, 100)));
    }
    
    return true;
  } catch (error) {
    console.error('Error upgrading subscription:', error);
    return false;
  }
}

// 获取订阅计划 - Get subscription plan
export function getSubscriptionPlan(planId: string): SubscriptionPlan | null {
  return SUBSCRIPTION_PLANS.find(plan => plan.id === planId) || null;
}

// 检查是否为高级用户 - Check if user is premium
export async function isPremiumUser(userId: string): Promise<boolean> {
  const subscription = await getUserSubscription(userId);
  return subscription ? ['basic', 'pro', 'premium'].includes(subscription.planId) : false;
}

// 获取用户当前积分 - Get user current credits
export async function getUserCredits(userId: string): Promise<number> {
  const subscription = await getUserSubscription(userId);
  return subscription ? subscription.credits : 0;
}
